﻿using HIH.Framework.AutoCrawingManager.Result;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace HIH.Framework.AutoCrawingManager.Process
{
    public class IMSC : IProcess
    {
        private readonly string SearchMainPage = "https://www.msccargo.cn/";

        public IMSC():base("MSC",""){}


        public override List<IProcessItem> Run(string searchKey,string replaceText)
        {
            List<IProcessItem> processList = new List<IProcessItem>();

            IProcessItem firPro = new IProcessItem(0, "跳转", IProcessType.JUMPTO);
            IProcessItem secPro = new IProcessItem(1, "输入", IProcessType.OPERATE);
            IProcessItem thiPro = new IProcessItem(2, "点击", IProcessType.OPERATE);
            IProcessItem fouPro = new IProcessItem(3, "解析", IProcessType.READ, this.GetResult);

            searchKey = string.IsNullOrEmpty(replaceText) ? searchKey : searchKey.Replace(replaceText, "");
            firPro.JScript = this.SearchMainPage;
            secPro.JScript = "let input = document.querySelector('.msc-quick-access-box__search');let inputEvent = new Event('input', { bubbles: true });if(input){input.value = '" + searchKey + "';input.dispatchEvent(inputEvent);}";
            thiPro.JScript = "let searchElements = document.querySelectorAll('.msc-search-autocomplete__search');console.log(searchElements[1]);if (searchElements.length > 1){let secondElement = searchElements[1]; const clickEvent = new Event('click', { bubbles: true });secondElement.dispatchEvent(clickEvent);}";
            fouPro.JScript = "document.documentElement.innerHTML";

            processList.Add(firPro);
            processList.Add(secPro);
            processList.Add(thiPro);
            processList.Add(fouPro);

            return processList;
        }

        private IResult GetResult(string resultString)
        {
            try
            {
                IResult result = new IResult();
                try
                {
                    result.ETA = this.GetETA(resultString);
                }
                catch (Exception exc)
                {
                    result.ETAExc = exc.Message;
                }
                try
                {
                    result.ContainerList = this.GetContainerItems(resultString);
                }
                catch (Exception exc)
                {
                    result.ContainerExc = exc.Message;
                }
                return result;
            }
            catch (Exception exc)
            {
                throw;
            }
        }


        private BindingList<IResultContainer> GetContainerItems(string contaienrString)
        {
            try
            {
                BindingList<IResultContainer> containerItemList = new BindingList<IResultContainer>();

                string containerPattern = @"<div[^>]*:data-index=""index""[^>]*:data-bill-index=""billIndex""[^>]*>(.*?)<\/template>[^>]*<\/div>[^>]*<\/div>[^>]*<\/div>[^>]*<\/div>[^>]*<\/div>";
                MatchCollection containerMatches = Regex.Matches(contaienrString, containerPattern, RegexOptions.Singleline);

                foreach (Match containerMatch in containerMatches)
                {
                    IResultContainer containerItem = new IResultContainer();
                    // 匹配所有跟踪节点
                    var portRegex = new Regex(
                        @"<div\s+class=""msc-flow-tracking__port""[^>]*>(.*?)</div>[^>]*</template>",
                        RegexOptions.Singleline | RegexOptions.IgnoreCase
                    );
                    // 匹配日期和描述的通用正则
                    var dateRegex = new Regex(
                        @"<span[^>]*x-text=""event.Date""[^>]*>(.*?)</span>",
                        RegexOptions.Singleline | RegexOptions.IgnoreCase
                    );
                    var descRegex = new Regex(
                        @"<span[^>]*x-text=""event.Description""[^>]*>(.*?)</span>",
                        RegexOptions.Singleline | RegexOptions.IgnoreCase
                    );

                    string containerNoPattern = @"<span[^>]*x-text=""container.ContainerNumber""[^>]*>(.*?)</span>";
                    MatchCollection containerNoMatches = Regex.Matches(containerMatch.Value, containerNoPattern, RegexOptions.Singleline);

                    string containerNo = "";
                    foreach (Match containerNoMatch in containerNoMatches)
                    {
                        if (!string.IsNullOrEmpty(containerNoMatch.Groups[1].Value.Trim().Replace(" ", "")))
                        {
                            containerNo = containerNoMatch.Groups[1].Value.Trim().Replace(" ", "");
                        }
                    }
                    if (string.IsNullOrEmpty(containerNo))
                        continue;

                    containerItem.ContainerNo = containerNo;

                    // 循环处理每个跟踪节点
                    foreach (Match portMatch in portRegex.Matches(containerMatch.Value))
                    {
                        string portContent = portMatch.Groups[1].Value;
                        string date = ExtractValue(dateRegex, portContent);
                        string description = ExtractValue(descRegex, portContent);

                        if (description == "Import to consignee")
                        {
                            containerItem.ContainerPickupTime = ParseExactTime(date);
                        }

                        if (description == "Import Discharged from Vessel")
                        {
                            containerItem.UnloadingTime = ParseExactTime(date);
                        }

                        if (description.StartsWith("Empty received"))
                        {
                            containerItem.ReturnTime = ParseExactTime(date);
                        }
                        Console.WriteLine(description);
                    }

                    containerItemList.Add(containerItem);

                }

                return containerItemList;
            }
            catch (Exception exc)
            {
                throw;
            }
        }

        private string ExtractValue(Regex regex, string content)
        {
            var match = regex.Match(content);
            return match.Success ? match.Groups[1].Value.Trim() : null;
        }


        private string GetETA(string result)
        {
            try
            {
                string contentPattern = @"<span[^>]*x-text=""[^""]*container.PodEtaDate""[^>]*>(.*?)<\/span>";

                MatchCollection matches = Regex.Matches(result, contentPattern, RegexOptions.Singleline);

                if (matches.Count <= 0)
                    throw new Exception("ETA未能爬取得到");

                string etaValue = "";
                foreach (Match match in matches)
                {
                    etaValue = match.Groups[1].Value;
                    if (!string.IsNullOrEmpty(etaValue))
                        break;
                }

                if (string.IsNullOrEmpty(etaValue))
                    throw new Exception("ETA未能爬取得到");

                return this.ParseExactTime(etaValue);

            }
            catch (Exception exc)
            {
                throw;
            }
        }

        private string ParseExactTime(string inputDate)
        {
            try
            {
                string format = "dd/MM/yyyy"; // 这里定义了日期字符串的格式

                DateTime parsedDate;

                if (DateTime.TryParseExact(inputDate, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out parsedDate))
                {
                    return parsedDate.ToString("yyyy-MM-dd");
                }
                else
                {
                    throw new Exception("解析日期失败【" + inputDate + "】");
                }

            }
            catch (Exception exc)
            {
                throw;
            }
        }




    }
}
