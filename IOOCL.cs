using HIH.Framework.AutoCrawingManager.Result;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace HIH.Framework.AutoCrawingManager.Process
{
    public class IOOCL : IProcess
    {
        private readonly string SearchMainPage = "https://www.oocl.com/schi/Pages/default.aspx";

        public IOOCL():base("OOCL","OOLU"){}

        public override List<IProcessItem> Run(string searchKey,string replaceText)
        {
            List<IProcessItem> processList = new List<IProcessItem>();

            IProcessItem firPro = new IProcessItem(0, "跳转", IProcessType.JUMPTO);
            IProcessItem secPro = new IProcessItem(1, "输入", IProcessType.OPERATE);
            IProcessItem thiPro = new IProcessItem(2, "点击", IProcessType.OPERATE);
            IProcessItem fouPro = new IProcessItem(3, "解析", IProcessType.READ, this.GetResult);

            searchKey = string.IsNullOrEmpty(replaceText) ? searchKey : searchKey.Replace(replaceText, "");
            firPro.JScript = this.SearchMainPage;
            secPro.JScript = "document.getElementById('SEARCH_NUMBER').value='" + searchKey + "';";
            thiPro.JScript = "ListeningCargoTrackingBtn();";
            fouPro.JScript = @"(function() {
                                    var eta = document.getElementById('form:arrivalDate0');
                                    if (eta) {
                                        return eta.innerHTML;
                                    } else {
                                        return '';
                                    }
                                })()";

            processList.Add(firPro);
            processList.Add(secPro);
            processList.Add(thiPro);
            processList.Add(fouPro);

            return processList;
        }

        private IResult GetResult(string resultString)
        {
            try
            {
                IResult result = new IResult();
                try
                {
                    string cleanedString = resultString.Trim('\"');
                    result.ETA = this.GetETA(cleanedString);
                }
                catch (Exception exc)
                {
                    result.ETAExc = exc.Message;
                }

                return result;
            }
            catch (Exception exc)
            {
                throw;
            }
        }


        private string GetETA(string result)
        {
            try
            {

                string etaValue = result;

                if (string.IsNullOrEmpty(etaValue))
                    throw new Exception("未找到正确的匹配对象");

                return this.ParseExactTime(etaValue);

            }
            catch (Exception exc)
            {
                throw;
            }
        }

        private string ParseExactTime(string dateTimeString)
        {
            try
            {
                string format = "";
               
                string cestFormat = "dd MMM yyyy, HH:mm 'CEST'";
                string edtFormat = "dd MMM yyyy, HH:mm 'EDT'";
                string estFormat = "dd MMM yyyy, HH:mm 'EST'";
                string pdtFormat = "dd MMM yyyy, HH:mm 'PDT'"; // 添加PDT格式
                if (dateTimeString.EndsWith("EST"))
                {
                    format = estFormat;
                }
                if (dateTimeString.EndsWith("EDT"))
                {
                    format = edtFormat;
                }
                if (dateTimeString.EndsWith("CEST"))
                {
                    format = cestFormat;
                }
                if (dateTimeString.EndsWith("PDT")) // 添加PDT判断
                {
                    format = pdtFormat;
                }


                if (string.IsNullOrEmpty(format))
                    throw new Exception("不正确的日期格式");

                DateTime parsedDate = DateTime.ParseExact(
                   dateTimeString,
                   format,
                   CultureInfo.InvariantCulture
                );


                string outputDate = parsedDate.ToString("yyyy-MM-dd");
                return outputDate;


            }
            catch (Exception exc)
            {
                throw;
            }
        }


    }
}
