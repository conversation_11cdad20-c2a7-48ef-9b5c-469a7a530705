﻿using HIH.Framework.AutoCrawingManager.Result;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace HIH.Framework.AutoCrawingManager.Process
{
    public class ICOSCO : IProcess
    {
        private readonly string SearchMainPage = "https://elines.coscoshipping.com/scct/public/ct/base?lang=zh&trackingType=BILLOFLADING&number=";

        public ICOSCO():base("COSCO","COSU"){}


        public override List<IProcessItem> Run(string searchKey,string replaceText)
        {
            List<IProcessItem> processList = new List<IProcessItem>();

            IProcessItem firPro = new IProcessItem(0, "跳转", IProcessType.JUMPTO);
            IProcessItem secPro = new IProcessItem(4, "解析", IProcessType.READ, this.GetResult);

            searchKey = string.IsNullOrEmpty(replaceText) ? searchKey : searchKey.Replace(replaceText, "");
            firPro.JScript = this.SearchMainPage + searchKey;
            secPro.JScript = "document.documentElement.innerHTML";

            processList.Add(firPro);
            processList.Add(secPro);

            return processList;
        }

        private IResult GetResult(string resultString)
        {
            try
            {
                IResult result = new IResult();
                try
                {
                    result.ETA = this.GetETA(resultString);
                }catch(Exception exc)
                {
                    result.ETAExc = exc.Message;
                }
                try
                {
                    result.ContainerList = this.GetContainerItems(resultString);
                }
                catch (Exception exc)
                {
                    result.ContainerExc = exc.Message;
                }
                return result;
            }
            catch(Exception exc)
            {
                throw;
            }
        }


        private BindingList<IResultContainer> GetContainerItems(string containerString)
        {
            try
            {
                BindingList<IResultContainer> containerList = new BindingList<IResultContainer>();

                string trPattern = @"<tr[^>]*class=""ant-table-row ant-table-row-level-0""[^>]*>(.*?)</tr>";

                MatchCollection containerMatches = Regex.Matches(containerString, trPattern, RegexOptions.Singleline);

                foreach (Match containerMatch in containerMatches)
                {
                    string tdContainerString = containerMatch.Value;
                    string tdPattern = @"<td[^>]*class=""ant-table-cell[^>]*>(.*?)<\/td>";
                    MatchCollection tdMatches = Regex.Matches(tdContainerString, tdPattern, RegexOptions.Singleline);
                    string containerNo = "";
                    string returnDateString = "";
                    string pickupDateString = "";
                    string uploadDateString = "";
                    foreach (Match tdMatch in tdMatches)
                    {
                        string td = tdMatch.Groups[1].Value.Replace("<!---->", "");
                        if (td.Contains("div"))
                        {
                            string divPattern = @"<div[^>]*>(.*?)<\/div>";
                            MatchCollection divMatches = Regex.Matches(td, divPattern, RegexOptions.Singleline);
                            if (divMatches.Count == 1)
                            {
                                containerNo = divMatches[0].Groups[1].Value;
                            }
                            if (divMatches.Count == 2)
                            {
                                if (divMatches[0].Groups[1].Value == "还空箱"
                                    ||
                                   divMatches[0].Groups[1].Value == "Empty Return")
                                {
                                    returnDateString = divMatches[1].Groups[1].Value.Replace("At", "").Replace("于", "").Trim();
                                }
                                if (divMatches[0].Groups[1].Value == "提取重箱"
                                    ||
                                   divMatches[0].Groups[1].Value == "Laden Pick up")
                                {
                                    pickupDateString = divMatches[1].Groups[1].Value.Replace("At", "").Replace("于", "").Trim();
                                }

                                if (divMatches[0].Groups[1].Value == "目的港卸货"
                                    ||
                                   divMatches[0].Groups[1].Value.StartsWith("Discharged"))
                                {
                                    uploadDateString = divMatches[1].Groups[1].Value.Replace("At", "").Replace("于", "").Trim();
                                }

                            }
                        }
                    }
                    if (!string.IsNullOrEmpty(containerNo))
                    {
                        IResultContainer containerItem = new IResultContainer();
                        Console.WriteLine(containerNo + "-" + pickupDateString + "-" + returnDateString);
                        containerItem.SetNewContainerItem(containerNo, pickupDateString, uploadDateString, returnDateString);
                        containerList.Add(containerItem);
                    }

                }
                return containerList;
            }
            catch (Exception exc)
            {
                throw;
            }
        }

        private string GetETA(string result)
        {
            try
            {

                string contentPattern = @"<div[^>]*class=""[^""]*main-content[^""]*""[^>]*>(.*?)<div[^>]*class=""[^""]*ant-flex css-ffhn8y ant-flex-align-center[^""]*""[^>]*>";

                MatchCollection matches = Regex.Matches(result, contentPattern, RegexOptions.Singleline);

                Console.WriteLine(matches);

                if (matches.Count <= 0)
                    throw new Exception("未找到正确的匹配对象");

                string matchVal = matches[0].Value;


                string liPattern = @"<span[^>]*class=""[^""]*date[^""]*""[^>]*>(.*?)<\/span>";

                MatchCollection liMatches = Regex.Matches(matchVal, liPattern, RegexOptions.Singleline);

                if (liMatches.Count <= 0) 
                    throw new Exception("未找到正确的匹配对象");


                //直接取ETA
                if (liMatches.Count > 3 && DateTime.TryParse(liMatches[2].Groups[1].Value, out DateTime etaDate))
                { 
                    return etaDate.ToString("yyyy-MM-dd");
                }
                else
                {
                    throw new Exception("未找到正确的匹配对象");
                }
            }catch (Exception exc)
            {
                throw;
            }
        }


    }
}
